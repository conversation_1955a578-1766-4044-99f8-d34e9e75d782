#!/usr/bin/env python3
"""
Simple debug script for path construction
"""
import os

def test_path_construction():
    """Debug path construction"""
    print("Testing path construction...")
    
    # Test regular path construction
    build_share = "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca"
    sub_path = "code"
    branch = "branch1"
    
    # This is what happens in the actual code
    path = os.path.join(build_share, '/', sub_path)
    print(f"Path construction: os.path.join('{build_share}', '/', '{sub_path}') = '{path}'")
    
    branch_path = os.path.join(path, '/', branch)
    print(f"Branch path: os.path.join('{path}', '/', '{branch}') = '{branch_path}'")
    
    # What it should be
    correct_path = os.path.join(build_share, sub_path)
    print(f"Correct path: os.path.join('{build_share}', '{sub_path}') = '{correct_path}'")
    
    correct_branch_path = os.path.join(correct_path, branch)
    print(f"Correct branch path: os.path.join('{correct_path}', '{branch}') = '{correct_branch_path}'")
    
    # Test the expected values from the test
    expected_path = "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1"
    print(f"Expected from test: '{expected_path}'")
    
    # Test if they match
    print(f"Current matches expected: {branch_path == expected_path}")
    print(f"Correct matches expected: {correct_branch_path == expected_path}")

if __name__ == "__main__":
    test_path_construction()
