#!/usr/bin/env python3
"""
Validation script to check if test paths have been fixed
"""
import re

def check_test_file():
    test_file = r"c:\Users\<USER>\vscode\pycharm\elipy-scripts\dice_elipy_scripts\tests\test_deleter.py"
    
    try:
        with open(test_file, 'r') as f:
            content = f.read()
        
        # Count forward slashes in paths that should be backslashes
        forward_slash_patterns = [
            r'fake-filer\.dice\.ad\.ea\.com\\builds\\Casablanca/code',
            r'fake-filer\.dice\.ad\.ea\.com\\builds\\Casablanca/frosty'
        ]
        
        forward_slash_matches = 0
        backslash_matches = 0
        
        for pattern in forward_slash_patterns:
            forward_slash_matches += len(re.findall(pattern, content))
        
        # Count proper backslash patterns
        backslash_patterns = [
            r'fake-filer\.dice\.ad\.ea\.com\\builds\\Casablanca\\code',
            r'fake-filer\.dice\.ad\.ea\.com\\builds\\Casablanca\\frosty'
        ]
        
        for pattern in backslash_patterns:
            backslash_matches += len(re.findall(pattern, content))
        
        print(f"Forward slash matches (should be 0): {forward_slash_matches}")
        print(f"Backslash matches (should be > 0): {backslash_matches}")
        
        if forward_slash_matches == 0 and backslash_matches > 0:
            print("✓ Test file appears to have been fixed correctly")
            return True
        else:
            print("✗ Test file still has issues")
            return False
            
    except Exception as e:
        print(f"Error reading file: {e}")
        return False

if __name__ == "__main__":
    check_test_file()
